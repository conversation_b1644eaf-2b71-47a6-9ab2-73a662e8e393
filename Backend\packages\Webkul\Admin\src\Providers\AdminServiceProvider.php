<?php

namespace Webkul\Admin\Providers;

use Illuminate\Support\Facades\Blade;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\ServiceProvider;
use Webkul\Core\Http\Middleware\PreventRequestsDuringMaintenance;

class AdminServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        $this->registerConfig();
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // 注册Admin语言切换中间件
        $router = $this->app['router'];
        $router->aliasMiddleware('admin.locale', \Webkul\Admin\Http\Middleware\AdminLocale::class);

        Route::middleware(['web', PreventRequestsDuringMaintenance::class])->group(__DIR__.'/../Routes/web.php');

        $this->loadTranslationsFrom(__DIR__.'/../Resources/lang', 'admin');

        $this->loadViewsFrom(__DIR__.'/../Resources/views', 'admin');

        Blade::anonymousComponentPath(__DIR__.'/../Resources/views/components', 'admin');

        // 注册后台翻译辅助函数
        $this->registerAdminTranslationHelpers();

        // 注册Blade指令
        $this->registerBladeDirectives();

        $this->app->register(EventServiceProvider::class);
    }

    /**
     * Register package config.
     */
    protected function registerConfig(): void
    {
        $this->mergeConfigFrom(
            dirname(__DIR__).'/Config/menu.php',
            'menu.admin'
        );

        $this->mergeConfigFrom(
            dirname(__DIR__).'/Config/acl.php',
            'acl'
        );

        $this->mergeConfigFrom(
            dirname(__DIR__).'/Config/system.php',
            'core'
        );
        
        $this->mergeConfigFrom(
            dirname(__DIR__).'/Config/homepage.php',
            'core'
        );
    }

    /**
     * 注册后台翻译辅助函数
     */
    protected function registerAdminTranslationHelpers(): void
    {
        if (!function_exists('admin_trans')) {
            /**
             * 后台专用翻译函数
             *
             * @param string $key
             * @param array $replace
             * @param string|null $locale
             * @return string
             */
            function admin_trans($key, $replace = [], $locale = null) {
                return \Webkul\Admin\Helpers\AdminTranslator::trans($key, $replace, $locale);
            }
        }

        if (!function_exists('admin_trans_choice')) {
            /**
             * 后台专用复数翻译函数
             *
             * @param string $key
             * @param int $number
             * @param array $replace
             * @param string|null $locale
             * @return string
             */
            function admin_trans_choice($key, $number, $replace = [], $locale = null) {
                $adminLocale = $locale ?: session()->get('admin_locale', 'en');
                return trans_choice($key, $number, $replace, $adminLocale);
            }
        }
    }

    /**
     * 注册Blade指令
     */
    protected function registerBladeDirectives(): void
    {
        // 注册 @admin_lang 指令，用于后台专用翻译
        Blade::directive('admin_lang', function ($expression) {
            return "<?php echo admin_trans($expression); ?>";
        });
    }
}
