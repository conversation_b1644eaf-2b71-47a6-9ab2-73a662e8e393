<?php

namespace Webkul\Admin\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class LocaleSwitchController extends Controller
{
    /**
     * 支持的语言列表
     */
    protected $supportedLocales = [
        'en' => [
            'code' => 'en',
            'name' => 'English',
            'flag' => '🇺🇸'
        ],
        'zh_CN' => [
            'code' => 'zh_CN', 
            'name' => '中文',
            'flag' => '🇨🇳'
        ],
        'it' => [
            'code' => 'it',
            'name' => 'Italiano', 
            'flag' => '🇮🇹'
        ]
    ];

    /**
     * 获取支持的语言列表
     *
     * @return JsonResponse
     */
    public function getSupportedLocales(): JsonResponse
    {
        return response()->json([
            'locales' => $this->supportedLocales,
            'current' => session()->get('admin_locale', config('app.locale', 'en'))
        ]);
    }

    /**
     * 切换语言
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function switchLocale(Request $request): JsonResponse
    {
        $locale = $request->input('locale');
        
        // 验证语言是否支持
        if (!array_key_exists($locale, $this->supportedLocales)) {
            return response()->json([
                'success' => false,
                'message' => 'Unsupported locale'
            ], 400);
        }
        
        // 将语言设置保存到session
        session()->put('admin_locale', $locale);
        
        return response()->json([
            'success' => true,
            'message' => 'Language switched successfully',
            'locale' => $this->supportedLocales[$locale]
        ]);
    }

    /**
     * 获取当前语言
     *
     * @return JsonResponse
     */
    public function getCurrentLocale(): JsonResponse
    {
        $currentLocale = session()->get('admin_locale', config('app.locale', 'en'));
        
        return response()->json([
            'current' => $currentLocale,
            'locale_info' => $this->supportedLocales[$currentLocale] ?? $this->supportedLocales['en']
        ]);
    }
}
