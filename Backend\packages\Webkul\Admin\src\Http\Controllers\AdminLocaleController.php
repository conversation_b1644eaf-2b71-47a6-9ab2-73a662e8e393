<?php

namespace Webkul\Admin\Http\Controllers;

use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class AdminLocaleController extends Controller
{
    /**
     * 支持的后台语言列表
     */
    private const SUPPORTED_LOCALES = [
        'en' => [
            'code' => 'en',
            'name' => 'English',
            'flag' => '🇺🇸'
        ],
        'zh_CN' => [
            'code' => 'zh_CN', 
            'name' => '中文',
            'flag' => '🇨🇳'
        ],
        'it' => [
            'code' => 'it',
            'name' => 'Italiano', 
            'flag' => '🇮🇹'
        ]
    ];

    /**
     * 获取支持的语言列表
     *
     * @return JsonResponse
     */
    public function getSupportedLocales(): JsonResponse
    {
        return response()->json([
            'success' => true,
            'data' => array_values(self::SUPPORTED_LOCALES),
            'current' => session()->get('admin_locale', config('app.locale', 'en'))
        ]);
    }

    /**
     * 切换后台语言
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function switchLocale(Request $request): JsonResponse
    {
        $localeCode = $request->input('locale');

        // 验证语言代码是否支持
        if (!$localeCode || !array_key_exists($localeCode, self::SUPPORTED_LOCALES)) {
            return response()->json([
                'success' => false,
                'message' => trans('admin::app.components.layouts.header.locale-switcher.invalid-locale')
            ], 400);
        }

        // 设置session中的语言
        session()->put('admin_locale', $localeCode);

        // 立即设置当前请求的语言环境（用于返回正确的响应消息）
        app()->setLocale($localeCode);

        return response()->json([
            'success' => true,
            'message' => trans('admin::app.components.layouts.header.locale-switcher.switched-success'),
            'locale' => self::SUPPORTED_LOCALES[$localeCode]
        ]);
    }

    /**
     * 获取当前语言信息
     *
     * @return JsonResponse
     */
    public function getCurrentLocale(): JsonResponse
    {
        $currentLocale = session()->get('admin_locale', config('app.locale', 'en'));
        
        // 确保当前语言在支持列表中
        if (!array_key_exists($currentLocale, self::SUPPORTED_LOCALES)) {
            $currentLocale = 'en';
        }

        return response()->json([
            'success' => true,
            'data' => self::SUPPORTED_LOCALES[$currentLocale]
        ]);
    }
}
