<?php

namespace Webkul\Admin\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class AdminLocale
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        // 定义后台支持的语言
        $supportedLocales = ['en', 'zh_CN', 'it'];
        
        // 从session获取管理员设置的语言
        $localeCode = session()->get('admin_locale');
        
        // 如果session中没有语言设置，或者语言不在支持列表中，使用默认语言
        if (!$localeCode || !in_array($localeCode, $supportedLocales)) {
            $localeCode = config('app.locale', 'en');
        }
        
        // 确保语言在支持列表中
        if (!in_array($localeCode, $supportedLocales)) {
            $localeCode = 'en';
        }
        
        // 设置应用程序的语言环境
        app()->setLocale($localeCode);
        
        return $next($request);
    }
}
