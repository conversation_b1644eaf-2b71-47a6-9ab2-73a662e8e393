<?php

namespace Webkul\Admin\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class AdminLocale
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        // 定义后台支持的语言
        $supportedLocales = ['en', 'zh_CN', 'it'];

        // 从session获取管理员设置的语言
        $adminLocale = session()->get('admin_locale');

        // 如果session中没有语言设置，或者语言不在支持列表中，使用英语作为后台默认语言
        if (!$adminLocale || !in_array($adminLocale, $supportedLocales)) {
            $adminLocale = 'en';
        }

        // 将后台语言设置保存到session，供辅助函数使用
        session()->put('admin_locale', $adminLocale);

        // 不改变应用的语言环境，保持数据查询使用系统默认语言
        // 后台界面翻译将通过 admin_trans() 函数实现

        return $next($request);
    }
}
