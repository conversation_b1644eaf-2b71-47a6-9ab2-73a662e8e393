<?php

namespace Webkul\Admin\Helpers;

use Illuminate\Support\Facades\Lang;

class AdminTranslator
{
    /**
     * 获取后台专用翻译
     *
     * @param string $key
     * @param array $replace
     * @param string|null $locale
     * @return string
     */
    public static function trans($key, $replace = [], $locale = null)
    {
        // 获取后台设置的语言
        $adminLocale = $locale ?: session()->get('admin_locale', 'en');
        
        // 尝试获取后台专用翻译
        $translation = Lang::get($key, $replace, $adminLocale);
        
        // 如果翻译不存在，回退到英语
        if ($translation === $key && $adminLocale !== 'en') {
            $translation = Lang::get($key, $replace, 'en');
        }
        
        return $translation;
    }
    
    /**
     * 检查翻译是否存在
     *
     * @param string $key
     * @param string|null $locale
     * @return bool
     */
    public static function has($key, $locale = null)
    {
        $adminLocale = $locale ?: session()->get('admin_locale', 'en');
        return Lang::has($key, $adminLocale);
    }
    
    /**
     * 获取当前后台语言
     *
     * @return string
     */
    public static function getLocale()
    {
        return session()->get('admin_locale', 'en');
    }
}
